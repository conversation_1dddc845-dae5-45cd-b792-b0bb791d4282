<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cosfo.mall.bill.mapper.BillProfitSharingMapper">
  <resultMap id="BaseResultMap" type="com.cosfo.mall.bill.model.po.BillProfitSharing">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="receiver_tenant_id" jdbcType="BIGINT" property="receiverTenantId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="transaction_id" jdbcType="VARCHAR" property="transactionId" />
    <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="success_time" jdbcType="TIMESTAMP" property="successTime" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="wx_order_id" jdbcType="VARCHAR" property="wxOrderId" />
    <result column="fail_reason" jdbcType="VARCHAR" property="failReason" />
    <result column="detail_id" jdbcType="VARCHAR" property="detailId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="business_type" property="businessType"/>
    <result column="huifu_id" property="huifuId"/>
    <result column="account_type" jdbcType="TINYINT" property="accountType"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, tenant_id, receiver_tenant_id, order_id, app_id, `type`, account, transaction_id, out_trade_no, price,
    `status`, success_time, description, wx_order_id, fail_reason, detail_id, create_time,
    update_time,
    business_type,
    huifu_id,
    account_type,
    profit_sharing_channel
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill_profit_sharing
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill_profit_sharing
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.bill.model.po.BillProfitSharing" useGeneratedKeys="true">
    insert into bill_profit_sharing (tenant_id, receiver_tenant_id, order_id, app_id,
      `type`, account, transaction_id, 
      out_trade_no, price, `status`, 
      success_time, description, wx_order_id, 
      fail_reason, detail_id, create_time, 
      update_time, business_type)
    values (#{tenantId,jdbcType=BIGINT}, #{receiverTenantId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{appId,jdbcType=VARCHAR},
      #{type,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, #{transactionId,jdbcType=VARCHAR}, 
      #{outTradeNo,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, #{status,jdbcType=TINYINT}, 
      #{successTime,jdbcType=TIMESTAMP}, #{description,jdbcType=VARCHAR}, #{wxOrderId,jdbcType=VARCHAR}, 
      #{failReason,jdbcType=VARCHAR}, #{detailId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{businessType})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.bill.model.po.BillProfitSharing" useGeneratedKeys="true">
    insert into bill_profit_sharing
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="receiverTenantId != null">
        receiver_tenant_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="account != null">
        account,
      </if>
      <if test="transactionId != null">
        transaction_id,
      </if>
      <if test="outTradeNo != null">
        out_trade_no,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="successTime != null">
        success_time,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="wxOrderId != null">
        wx_order_id,
      </if>
      <if test="failReason != null">
        fail_reason,
      </if>
      <if test="detailId != null">
        detail_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="huifuId != null">
        huifu_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="receiverTenantId != null">
        #{receiverTenantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="transactionId != null">
        #{transactionId,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null">
        #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="successTime != null">
        #{successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="wxOrderId != null">
        #{wxOrderId,jdbcType=VARCHAR},
      </if>
      <if test="failReason != null">
        #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="detailId != null">
        #{detailId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessType != null">
        #{businessType},
      </if>
      <if test="huifuId != null">
        #{huifuId},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cosfo.mall.bill.model.po.BillProfitSharing">
    update bill_profit_sharing
    <set>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="receiverTenantId != null">
        receiver_tenant_id = #{receiverTenantId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="transactionId != null">
        transaction_id = #{transactionId,jdbcType=VARCHAR},
      </if>
      <if test="outTradeNo != null">
        out_trade_no = #{outTradeNo,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="successTime != null">
        success_time = #{successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="wxOrderId != null">
        wx_order_id = #{wxOrderId,jdbcType=VARCHAR},
      </if>
      <if test="failReason != null">
        fail_reason = #{failReason,jdbcType=VARCHAR},
      </if>
      <if test="detailId != null">
        detail_id = #{detailId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessType != null">
        business_type = #{businessType},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cosfo.mall.bill.model.po.BillProfitSharing">
    update bill_profit_sharing
    set tenant_id = #{tenantId,jdbcType=BIGINT},
      receiver_tenant_id = #{receiverTenantId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      app_id = #{appId,jdbcType=VARCHAR},
      `type` = #{type,jdbcType=VARCHAR},
      account = #{account,jdbcType=VARCHAR},
      transaction_id = #{transactionId,jdbcType=VARCHAR},
      out_trade_no = #{outTradeNo,jdbcType=VARCHAR},
      price = #{price,jdbcType=DECIMAL},
      `status` = #{status,jdbcType=TINYINT},
      success_time = #{successTime,jdbcType=TIMESTAMP},
      description = #{description,jdbcType=VARCHAR},
      wx_order_id = #{wxOrderId,jdbcType=VARCHAR},
      fail_reason = #{failReason,jdbcType=VARCHAR},
      detail_id = #{detailId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      business_type = #{businessType}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="querySuccessByTenantIdAndOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    bill_profit_sharing
    where tenant_id = #{tenantId} and order_id = #{orderId} and business_type = #{businessType} and status = 1
  </select>

  <update id="batchUpdateBillProFitSharing" >
    update
    bill_profit_sharing
    <set>
      <if test="sharingUpdate.status != null">
        `status` = #{sharingUpdate.status,jdbcType=TINYINT},
      </if>
      <if test="sharingUpdate.successTime != null">
        success_time = #{sharingUpdate.successTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sharingUpdate.wxOrderId != null">
        wx_order_id = #{sharingUpdate.wxOrderId,jdbcType=VARCHAR},
      </if>
      <if test="sharingUpdate.failReason != null">
        fail_reason = #{sharingUpdate.failReason,jdbcType=VARCHAR},
      </if>
      <if test="sharingUpdate.detailId != null">
        detail_id = #{sharingUpdate.detailId,jdbcType=VARCHAR},
      </if>
    </set>
    where id in
    <foreach collection="ids" item="id" close=")" open="(" separator=",">
      #{id}
    </foreach>
  </update>

  <select id="queryByTenantIdAndOutTradeNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"></include>
    from
    bill_profit_sharing
    where tenant_id = #{tenantId} and out_trade_no = #{outTradeNo} and business_type = #{businessType}
  </select>

  <insert id="saveBatch" keyColumn="id" keyProperty="id" parameterType="com.cosfo.mall.payment.model.po.RefundAcctSplitDetail"
          useGeneratedKeys="true">
    insert into bill_profit_sharing (tenant_id, receiver_tenant_id, order_id, `type`, account, business_type,
    out_trade_no, transaction_id, price, status, after_sale_id, account_type, profit_sharing_channel)
    values
    <foreach collection="billProfitSharings" item="item" separator="," >
      (#{item.tenantId}, #{item.receiverTenantId}, #{item.orderId}, #{item.type}, #{item.account}, #{item.businessType},
      #{item.outTradeNo}, #{item.transactionId}, #{item.price}, #{item.status}, #{item.afterSaleId},
      #{item.accountType}, #{item.profitSharingChannel})
    </foreach>
  </insert>
</mapper>