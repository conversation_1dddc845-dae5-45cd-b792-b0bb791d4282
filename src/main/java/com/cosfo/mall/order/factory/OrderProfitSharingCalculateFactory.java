package com.cosfo.mall.order.factory;

import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.order.service.ProfitSharingCalculate;
import com.cosfo.mall.order.service.impl.*;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;

/**
 * 描述: 工厂类
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/23
 */
@Component
public class OrderProfitSharingCalculateFactory {
    private static List<ProfitSharingCalculate> profitSharingCalculates = new LinkedList<>();
    /**
     * 自营仓
     */
    @Resource
    private ProprietaryProductProfitSharingCalculate proprietaryProductProfitSharingCalculate;
    /**
     * 手续费
     */
    @Resource
    private ServiceFeeProfitSharingCalculate serviceFeeProfitSharingCalculate;
    /**
     * 鲜沐供应品
     */
    @Resource
    private SupplyProductCalculate supplyProductCalculate;
    /**
     * 运费
     */
    @Resource
    private DeliveryFeeProfitSharingCalculate deliveryFeeProfitSharingCalculate;

    /**
     * 订单金额全部分账给个人
     */
    @Resource
    private SharingToPersonalProfitSharingCalculate sharingToPersonalProfitSharingCalculate;

    @Resource
    private NoWarehouseProductProfitSharingCalculate noWarehouseProductProfitSharingCalculate;

    @PostConstruct
    public void init() {
        profitSharingCalculates.add(proprietaryProductProfitSharingCalculate);
        profitSharingCalculates.add(serviceFeeProfitSharingCalculate);
        profitSharingCalculates.add(supplyProductCalculate);
        profitSharingCalculates.add(deliveryFeeProfitSharingCalculate);
        profitSharingCalculates.add(sharingToPersonalProfitSharingCalculate);
        profitSharingCalculates.add(noWarehouseProductProfitSharingCalculate);
    }

    /**
     * 获取策略执行器
     *
     * @param deliveryType 配送方式0品牌方1三方仓
     * @param profitSharingRuleType 分账金额类型分账金额类型 1,自营商品金额2，供应商商品金额3，代仓商品金额4运费5订单手续费
     * @param type  0部分分账给品牌方1全部分账给品牌方
     * @return
     */
    public static ProfitSharingCalculate get(Integer deliveryType, Integer profitSharingRuleType, Integer type) {
        for (ProfitSharingCalculate profitSharingCalculate: profitSharingCalculates) {
            if (profitSharingCalculate.support(deliveryType, profitSharingRuleType, type)) {
                return profitSharingCalculate;
            }
        }

        throw new BizException(ResultDTOEnum.PROFIT_SHARING_HANDLE_NOT_EXIST.getCode(), ResultDTOEnum.PROFIT_SHARING_HANDLE_NOT_EXIST.getMessage());
    }
}
