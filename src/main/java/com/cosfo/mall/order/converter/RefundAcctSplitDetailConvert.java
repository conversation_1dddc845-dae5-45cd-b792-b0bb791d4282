package com.cosfo.mall.order.converter;

import com.cosfo.mall.payment.model.dto.RefundAcctSplitDetailDTO;
import com.cosfo.mall.payment.model.po.RefundAcctSplitDetail;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/21
 */
@Mapper
public interface RefundAcctSplitDetailConvert {

    RefundAcctSplitDetailConvert INSTANCE = Mappers.getMapper(RefundAcctSplitDetailConvert.class);

    /**
     * 转化为RefundAcctSplitDetailDTOList
     *
     * @param refundAcctSplitDetailList
     * @return
     */
    List<RefundAcctSplitDetailDTO> toRefundAcctSplitDetailDTOList(List<RefundAcctSplitDetail> refundAcctSplitDetailList);
}
