package com.cosfo.mall.payment.controller;

import cn.hutool.http.server.HttpServerRequest;
import com.cosfo.mall.payment.service.DinNotifyService;
import com.cosfo.mall.payment.service.PaymentService;
import com.cosfo.mall.payment.service.RefundService;
import com.cosfo.mall.wechat.bean.notify.DirectNotify;
import com.cosfo.mall.wechat.bean.notify.NotifyResponse;
import net.summerfarm.payment.trade.adapter.dinpay.dto.notify.DinNotifyDTO;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2022/5/23  17:32
 */
@RestController
public class NotifyController {
    @Resource
    private PaymentService paymentService;
    @Resource
    private RefundService refundService;
    @Resource
    private DinNotifyService dinNotifyService;

    @RequestMapping(value = "/pay-notify/wx-direct", method = RequestMethod.POST)
    public NotifyResponse wxDirectPayNotify(@RequestBody DirectNotify directNotify, HttpServerRequest request) {
        return paymentService.wxDirectPayNotify(directNotify, request);
    }

    /**
     * 汇付聚合正扫异步通知接口
     *
     * @param request
     * @return
     */
    @RequestMapping("/pay-notify/huifu-pay")
    public String huiFuPayNotification(HttpServletRequest request){
        return paymentService.huiFuPayNotification(request);
    }

    /**
     * 汇付退款回调
     * @param request 退款回调
     * @return
     */
    @RequestMapping("/pay-notify/huifu-refund")
    public String huiFuRefundNotify(HttpServletRequest request) {
        return refundService.handleHuiFuRefundNotify(request);
    }

    /**
     * 智付支付回调
     *
     * @param dinNotifyDTO
     * @return
     */
    @RequestMapping(value = "/pay-notify/din-pay", method = RequestMethod.POST, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public String dinPayNotify(@ModelAttribute DinNotifyDTO dinNotifyDTO) {
        return dinNotifyService.payNotify(dinNotifyDTO);
    }

    /**
     * 智付退款回调
     *
     * @param dinNotifyDTO
     * @return
     */
    @RequestMapping(value = "/pay-notify/din-refund", method = RequestMethod.POST, consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public String dinRefundNotify(@ModelAttribute DinNotifyDTO dinNotifyDTO) {
        return dinNotifyService.refundNotify(dinNotifyDTO);
    }
}
