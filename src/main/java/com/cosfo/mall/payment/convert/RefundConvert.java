package com.cosfo.mall.payment.convert;

import com.cosfo.mall.wechat.bean.refund.RefundNotify;
import com.cosfo.mall.wechat.bean.refund.RefundResult;

/**
 * @description:
 * @author: George
 * @date: 2023-09-08
 **/
public class RefundConvert {

    public static RefundNotify convertRefundNotify(RefundResult result) {
        RefundNotify refundNotify = new RefundNotify();
        refundNotify.setTransactionId(result.getTransactionId());
        refundNotify.setOutTradeNo(result.getOutTradeNo());
        refundNotify.setRefundId(result.getRefundId());
        refundNotify.setOutRefundNo(result.getOutRefundNo());
        refundNotify.setRefundStatus(result.getStatus());
        refundNotify.setSuccessTime(result.getSuccessTime());
        refundNotify.setUserReceivedAccount(result.getUserReceivedAccount());
        refundNotify.setAmount(result.getAmount());
        return refundNotify;
    }
}
