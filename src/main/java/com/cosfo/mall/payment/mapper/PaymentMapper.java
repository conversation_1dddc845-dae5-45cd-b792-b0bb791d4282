package com.cosfo.mall.payment.mapper;

import com.cosfo.mall.payment.model.po.Payment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

@Mapper
public interface PaymentMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Payment record);

    int insertSelective(Payment record);

    Payment selectByPrimaryKey(Long id);

    Payment selectByPrimaryKeyForUpdate(Long id);

    int updateByPrimaryKeySelective(Payment record);

    int updateByPrimaryKey(Payment record);

    /**
     * 查询支付单
     *
     * @param paymentNo 支付单号
     * @return 支付单
     */
    Payment selectByPaymentNo(@Param("paymentNo") String paymentNo);

    /**
     * 乐观更新支付单费率
     * @param updatePayment
     * @return
     */
    int updateFeeByPrimaryKeyCas(Payment updatePayment);

    /**
     * 乐观更新支付单状态
     * @param id
     * @param currentStatus
     * @param beforeStatus
     * @return
     */
    int updateStatus(@Param("id") Long id, @Param("currentStatus") Integer currentStatus, @Param("beforeStatus") Integer beforeStatus);

    /**
     * 批量查询
     *
     * @param ids
     * @return
     */
    List<Payment> batchQueryByIdsForUpdate(@Param("ids") List<Long> ids);


    /**
     * 查询支付成功和处理中的支付单
     *
     * @param allPaymentIds
     * @return
     */
    List<Payment> querySuccessAndInProcessPaymentsForUpdate(@Param("ids") Collection<Long> allPaymentIds);

    /**
     * 批量更新支付单状态
     *
     * @param ids
     * @param currentStatus
     * @param beforeStatus
     * @return
     */
    int updateStatusByIds(@Param("ids") Collection<Long> ids, @Param("currentStatus") Integer currentStatus, @Param("beforeStatus") Integer beforeStatus);

    /**
     * 查询成功的支付单
     *
     * @param tenantIds
     * @param startTime
     * @param endTime
     * @return
     */
    List<Payment> querySuccessPaymentsByTenantId(@Param("tenantIds") Collection<Long> tenantIds, @Param("startTime") String startTime, @Param("endTime") String endTime);

    List<Payment> querySuccessPayments(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 根据时间查询成功的支付单
     *
     * @param startTime
     * @param endTime
     * @return
     */
    List<Payment> querySuccessPaymentsByTime(@Param("startTime") String startTime, @Param("endTime") String endTime);

    List<Payment> queryDuplicatePayments(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 根据订单号查询成功的支付单
     *
     * @param tenantId
     * @param orderId
     * @return
     */
    Payment querySuccessByOrderId(@Param("tenantId") Long tenantId, @Param("orderId") Long orderId);

}