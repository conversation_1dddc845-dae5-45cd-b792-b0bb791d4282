package com.cosfo.mall.payment.mapper;

import com.cosfo.mall.payment.model.dto.HuifuRefundUpdateDTO;
import com.cosfo.mall.payment.model.po.Refund;
import java.util.Collection;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface RefundMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Refund record);

    int insertSelective(Refund record);

    Refund selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Refund record);

    int updateByPrimaryKey(Refund record);

    int updateByRefundNoSelective(Refund record);

    Refund selectByRefundNo(@Param("refundNo") String refundNo);

    Refund selectByRefundId(@Param("refundId") String refundId);

    List<Refund> selectByNeedRetryList(@Param("startTime") LocalDateTime startTime,
                                       @Param("endTime") LocalDateTime endTime,
                                       @Param("statusList") List<Integer> statusList,
                                       @Param("retryNum") Integer retryNum,
                                       @Param("limitSize") Integer limitSize);


    List<Refund> selectByNeedRetryColdList(@Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime,
                                           @Param("status") Integer status,
                                           @Param("retryNum") Integer retryNum);

    int increaseRetryNumByIdAndStatus(@Param("id") Long id, @Param("status") Integer status);

    int increaseRetryNumById(@Param("id") Long id);

    /**
     * 通过数据库锁保证幂等
     * @param tenantId
     * @param afterSaleId
     * @return
     */
    Refund selectByAfterSaleId(@Param("tenantId") Long tenantId, @Param("afterSaleId") Long afterSaleId);

    /**
     * 乐观变更售后单状态
     * @param id 退款单id
     * @param beforeStatus 之前状态
     * @param newStatus 要更新到的状态
     * @return
     */
    int updateStatusCas(@Param("id") Long id,@Param("beforeStatus") Integer beforeStatus,@Param("newStatus") Integer newStatus);

    /**
     * 乐观变更汇付售后单请求id
     * @param huifuRefundUpdateDTO
     * @return
     */
    int updateRefundReqIdCas(HuifuRefundUpdateDTO huifuRefundUpdateDTO);

    /**
     * 通过数据库锁保证查询的是最新数据
     * @param id
     * @return
     */
    Refund selectByIdForUpdate(Long id);

    /**
     * 乐观锁更新逆向退款请求Id
     *
     * @param id
     * @param oldConfirmRefundReqId
     * @param newConfirmRefundReqId
     * @return
     */
    int updateConfirmRefundReqIdCas(@Param("id") Long id,
                                    @Param("oldConfirmRefundReqId") String oldConfirmRefundReqId,
                                    @Param("newConfirmRefundReqId") String newConfirmRefundReqId);

    /**
     * 批量生成退款单
     *
     * @param refunds
     */
    int batchInsert(List<Refund> refunds);

    /**
     * 根据时间查询成功的退款单
     * @param tenantIds
     * @param startTime
     * @param endTime
     * @return
     */
    List<Refund> querySuccessRefundsByTenant(@Param("tenantIds") Collection<Long> tenantIds, @Param("startTime") String startTime, @Param("endTime") String endTime);


    List<Refund> querySuccessRefund(@Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 根据售后单id查询
     *
     * @param tenantId
     * @param orderAfterSaleIds
     * @return
     */
    List<Refund> queryAfterSaleIds(@Param("tenantId") Long tenantId, @Param("orderAfterSaleIds") Collection<Long> orderAfterSaleIds);

}