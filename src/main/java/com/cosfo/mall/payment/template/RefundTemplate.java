package com.cosfo.mall.payment.template;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.bill.model.bo.RefundCalculateResultBO;
import com.cosfo.mall.bill.service.BillProfitSharingRefundService;
import com.cosfo.mall.common.constants.RedisKeyEnum;
import com.cosfo.mall.common.context.OutRefundResultEnum;
import com.cosfo.mall.common.context.RefundEnum;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.model.request.RefundExecuteRequest;
import com.cosfo.mall.payment.model.request.RefundRequest;
import com.cosfo.mall.payment.model.result.RefundExecuteResult;
import com.cosfo.mall.payment.model.result.RefundResult;
import com.cosfo.mall.payment.utils.RefundComponent;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * @description: 退款模板
 * @author: George
 * @date: 2023-09-01
 **/
@Slf4j
public abstract class RefundTemplate {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private BillProfitSharingRefundService billProfitSharingRefundService;
    @Resource
    private RefundComponent refundComponent;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;
    @Resource
    private PlatformTransactionManager transactionManager;

    /**
     * 生成退款信息
     * 不实际退款
     *
     * @param request
     */
    public RefundResult generateRefundInfo(RefundRequest request) {
        // 加锁
        RLock lock = lock(request);

        RefundResult result = RefundResult.builder().isSuccess(true).build();
        try {
            // 预处理
            preProcess(request, result);
            if (!result.isSuccess()) {
                return result;
            }
            // 保存退款信息 事务操作
            createRefundInTransaction(request);
        } finally {
            lock.unlock();
        }
        return result;
    }

    /**
     * 保存退款信息 事务操作
     *
     * @param request
     */
    private void createRefundInTransaction(RefundRequest request) {
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus transactionStatus = transactionManager.getTransaction(def);
        try {
            RefundCalculateResultBO refundCalculateResultBO = billProfitSharingRefundService.doRefundSharing(request.getOrderAfterSaleId());
            if (refundCalculateResultBO != null) {
                request.setRefundAcctSplitDetailDTOList(refundCalculateResultBO.getRefundAcctSplitDetailDTOS());
            }
            // 生成退款单
            createRefundOrder(request);
            // 生成退款的订单明细交易记录
            refundComponent.generateOrderItemFee(request.getOrderAfterSaleId());
            // 提交事务
            transactionManager.commit(transactionStatus);
        } catch (Exception e) {
            transactionManager.rollback(transactionStatus);
            log.error("创建退款单失败，refundRequest:[{}]", JSON.toJSONString(request), e);
            throw new ProviderException("创建退款单信息失败");
        }
    }

    /**
     * 生成退款单
     *
     * @param request
     * @return
     */
    protected abstract Long createRefundOrder(RefundRequest request);

    /**
     * 加锁
     *
     * @param request
     * @return
     */
    private RLock lock(RefundRequest request) {
        // 订单维度加锁 主要考虑是像分账退款、预付，都需要订单维度
        String redisKey = RedisKeyEnum.C00003.join(request.getOrderId());
        RLock lock = redissonClient.getLock(redisKey);
        // 未获取到锁，退出
        try {
            if (!lock.tryLock(1, TimeUnit.MINUTES)) {
                throw new ProviderException("该售后单正在处理退款请求，请稍后");
            }
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return lock;
    }

    /**
     * 预处理操作
     *
     * @param request
     */
    protected void preProcess(RefundRequest request, RefundResult result) {
        Long orderAfterSaleId = request.getOrderAfterSaleId();
        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByIds(Lists.newArrayList(orderAfterSaleId)));
        OrderAfterSaleResp orderAfterSale = afterSaleDTOList.get(0);
        if (Objects.isNull(orderAfterSale)) {
            result.setSuccess(false);
            log.error("未查询到售后单");
            return;
        }
        if (OrderAfterSaleStatusEnum.AUDITED_SUCCESS.getValue().equals(orderAfterSale.getStatus())) {
            result.setSuccess(false);
            log.error("售后订单已完成，售后信息：[{}]", JSON.toJSONString(orderAfterSale));
            return;
        }
        Refund refund = refundMapper.selectByAfterSaleId(request.getTenantId(), request.getOrderAfterSaleId());
        if (Objects.nonNull(refund)) {
            result.setSuccess(false);
            log.error("售后订单：[{}]已存在退款单：[{}]", request.getOrderAfterSaleId(), refund.getId());
        }
    }


    /**
     * 执行实际退款
     */
    public void executeRefund(RefundExecuteRequest request) {
        // 对退款单进行校验
        boolean checkResult = preProcessRefund(request);
        if (!checkResult) {
            return;
        }

        // 处理上次退款单
        OutRefundResultEnum lastRefundResultStatus = doLastRefundResult(request);
        if (lastRefundResultStatus != null && OutRefundResultEnum.FAIL != lastRefundResultStatus) {
            return;
        }
        // 实际去退款
        RefundExecuteResult result = processRefund(request);
        if (result.isSuccess()) {
            onSuccess(request, result);
        } else {
            onFailure(request, result);
        }
    }

    protected abstract void onFailure(RefundExecuteRequest request, RefundExecuteResult result);

    protected abstract void onSuccess(RefundExecuteRequest request, RefundExecuteResult result);

    protected abstract RefundExecuteResult processRefund(RefundExecuteRequest request);


    /**
     * 处理上次退款结果
     *
     * @param request
     * @return
     */
    protected abstract OutRefundResultEnum doLastRefundResult(RefundExecuteRequest request);

    /**
     * 预处理退款 主要做校验
     *
     * @param request
     * @return
     */
    private boolean preProcessRefund(RefundExecuteRequest request) {
        Refund refund = request.getRefund();

        // 乐观更新为退款中状态
        if (RefundEnum.Status.CREATE_REFUND.getStatus().equals(refund.getRefundStatus())) {
            int result = refundMapper.updateStatusCas(refund.getId(), RefundEnum.Status.CREATE_REFUND.getStatus(), RefundEnum.Status.IN_REFUND.getStatus());
            if (result <= 0) {
                log.error("退款重试定时任务 乐观更新为退款中状态失败本次不处理,refund:{}", JSON.toJSONString(refund));
                throw new ProviderException("乐观更新为退款中失败，暂不退款");
            }
        }
        return true;
    }

}
