package com.cosfo.mall.payment.template.huifu.alipay;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.common.utils.UserLoginContextUtil;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import com.cosfo.mall.payment.template.huifu.HuiFuPaymentTemplate;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.service.TenantService;
import com.qiniu.common.Zone;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.exception.ProviderException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2023-09-01
 **/
@Service
@Slf4j
public class HuiFuAliPayment extends HuiFuPaymentTemplate {

    @Resource
    private TenantService tenantService;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private HuiFuPaymentMapper huiFuPaymentMapper;

    @Override
    protected void preProcess(PaymentRequest request) {
        super.preProcess(request);
        // 汇付微信
        LoginContextInfoDTO loginContextInfoDTO = UserLoginContextUtil.getRequestContextInfoDTO();
        TenantAuthConnectionDTO tenantAuthConnectionDTO = tenantService.queryTenantAuthConnection(loginContextInfoDTO.getTenantId());
        Integer aliIndirectSwitch = tenantAuthConnectionDTO.getAliIndirectSwitch();
        if (aliIndirectSwitch == PaymentEnum.Switch.CLOSE.getType()) {
            throw new BizException("您暂无支付宝支付权限");
        }
        if (!request.getH5Request()) {
            throw new BizException("小程序暂不支持支付宝支付，请选择公众号或者浏览器");
        }
    }

    @Override
    protected PaymentResult assemblyPaymentResult(PaymentRequest request, PaymentResult result) {
        super.assemblyPaymentResult(request, result);
        Long paymentId = request.isCombineRequest() ? request.getMasterPaymentId() : request.getPaymentId();
        Payment payment = paymentMapper.selectByPrimaryKey(paymentId);
        LocalDateTime createTime = payment.getCreateTime();
        ZoneId zoneId = ZoneId.of("Asia/Shanghai");
        long timeStamp = createTime.atZone(zoneId).toEpochSecond();
        result.setTimeStamp(String.valueOf(timeStamp));
        if (Objects.isNull(result.getQrCode())) {
            HuiFuPayment huiFuPayment = huiFuPaymentMapper.selectOne(new LambdaQueryWrapper<HuiFuPayment>().eq(HuiFuPayment::getPaymentId, result.getPaymentId()));
            result.setQrCode(huiFuPayment.getQrCode());
        }
        return result;
    }
}
