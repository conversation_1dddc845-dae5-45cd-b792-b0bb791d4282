package com.cosfo.mall.payment.template.wechat;

import cn.hutool.core.math.Money;
import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.config.FanTaiPaymentConfig;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.context.OutRefundResultEnum;
import com.cosfo.mall.common.context.RefundEnum;
import com.cosfo.mall.common.utils.Global;
import com.cosfo.mall.order.service.OrderAfterSaleService;
import com.cosfo.mall.payment.convert.RefundConvert;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentItem;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.model.request.RefundExecuteRequest;
import com.cosfo.mall.payment.model.request.RefundRequest;
import com.cosfo.mall.payment.model.request.WechatRefundExecuteRequest;
import com.cosfo.mall.payment.model.result.RefundExecuteResult;
import com.cosfo.mall.payment.service.RefundService;
import com.cosfo.mall.payment.template.RefundTemplate;
import com.cosfo.mall.tenant.model.dto.TenantAuthConnectionDTO;
import com.cosfo.mall.tenant.service.TenantAuthConnectionService;
import com.cosfo.mall.tenant.service.TenantBillService;
import com.cosfo.mall.wechat.api.PayMchAPI;
import com.cosfo.mall.wechat.bean.base.Amount;
import com.cosfo.mall.wechat.bean.refund.RefundNotify;
import com.cosfo.mall.wechat.bean.refund.RefundResult;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleCommandProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleUpdateReq;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.BizException;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * @description: 微信退款
 * @author: George
 * @date: 2023-09-08
 **/
@Slf4j
@Service
public class WechatRefund extends RefundTemplate {

    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private RefundMapper refundMapper;
    @Resource
    private FanTaiPaymentConfig fanTaiPaymentConfig;
    @Resource
    private TenantAuthConnectionService tenantAuthConnectionService;
    @Resource
    private RefundService refundService;
    @Value("${notify-domain}")
    private String notifyDomain;
    @Resource
    private TransactionTemplate transactionTemplate;
    @Resource
    private TenantBillService tenantBillService;
    @Resource
    private OrderAfterSaleService orderAfterSaleService;

    @DubboReference
    private OrderAfterSaleCommandProvider orderAfterSaleCommandProvider;

    /**
     * 创建退款单
     *
     * @param request
     * @return
     */
    @Override
    protected Long createRefundOrder(RefundRequest request) {
        PaymentItem item = paymentItemMapper.selectPaySuccessByOrderId(request.getTenantId(), request.getOrderId());
        Payment payment = paymentMapper.selectByPrimaryKey(item.getPaymentId());
        String refundNo = Global.generateRefundNo();
        Refund refund = new Refund();
        refund.setTenantId(request.getTenantId());
        refund.setAfterSaleId(request.getOrderAfterSaleId());
        refund.setSubMchid(Objects.isNull(payment) ? null : payment.getSpMchid());
        refund.setRefundNo(refundNo);
        refund.setPaymentPrice(Objects.isNull(payment) ? null : payment.getTotalPrice());
        refund.setCreateTime(LocalDateTime.now());
        refund.setRefundStatus(RefundEnum.Status.CREATE_REFUND.getStatus());
        refund.setRefundPrice(request.getRefundPrice());
        refund.setPaymentId(Optional.ofNullable(payment).map(Payment::getId).orElse(null));
//        refund.setPaymentId(payment.getId());
        refundMapper.insertSelective(refund);
        return refund.getId();

    }

    @Override
    protected OutRefundResultEnum doLastRefundResult(RefundExecuteRequest request) {
        Refund refund = request.getRefund();
        Payment payment = request.getPayment();
        String payCertPath;
        if (payment.getSpMchid().equals(fanTaiPaymentConfig.getMchId())) {
            payCertPath = fanTaiPaymentConfig.getPayCertPath();
        } else {
            TenantAuthConnectionDTO authConnectionDTO = tenantAuthConnectionService.queryHistoryPayConfig(payment.getTenantId(), null, payment.getSpMchid());
            payCertPath = authConnectionDTO.getPayCertPath();
        }
        request.setPayCertPath(payCertPath);

        // 首次退款，无需查询结果
        if (NumberConstant.ZERO.equals(refund.getRetryNum())) {
            return OutRefundResultEnum.FAIL;
        }

        RefundResult result;
        try {
            result = PayMchAPI.queryWeChatRefundResultRequest(refund.getRefundNo(), payment.getSpMchid(), payCertPath);
        } catch (BizException bizException) {
            log.info("BizException refundNo:{},bizException.status:{}", refund.getRefundNo(), bizException.getErrorCode().getStatus());
            // 查询不到退款单,同样认为失败，可以发起重试
            // (原因:1、请求时并发拦截所以微信没有对应单号;2、发起请求时网络问题导致微信未接收到此退款单号)
            if (bizException.getErrorCode().getStatus() == HttpStatus.HTTP_NOT_FOUND) {
                return OutRefundResultEnum.FAIL;
            }
            throw bizException;
        }

        // 成功,流转通知
        if (RefundEnum.WxNotifyStatus.SUCCESS.getWxCode().equals(result.getStatus())) {
            RefundNotify refundNotify = RefundConvert.convertRefundNotify(result);
            log.info("通过定时任务发起的回调补偿,result:{},refundNotify:{}", JSON.toJSONString(result), JSON.toJSONString(refundNotify));
            refundService.handleWxRefundNotify(refundNotify);
            return OutRefundResultEnum.SUCCESS;
        }
        if (RefundEnum.WxNotifyStatus.CLOSED.getWxCode().equals(result.getStatus())) {
            return OutRefundResultEnum.FAIL;
        }
        return OutRefundResultEnum.PROCESSING;
    }

    @Override
    protected RefundExecuteResult processRefund(RefundExecuteRequest request) {
        // 组装退款信息
        WechatRefundExecuteRequest executeRequest = buildExecuteRequest(request);
        // 调用退款
        RefundResult result = PayMchAPI.refundRequest(executeRequest);
        // 处理退款结果
        processResult(request, result);
        return RefundExecuteResult.builder().isSuccess(true).build();
    }

    private void processResult(RefundExecuteRequest request, RefundResult result) {
        // 执行事务操作,处理结果数据
        transactionTemplate.execute(status -> {
            Boolean execute = true;
            try {
                handleWXRefundResult(request, result);
            } catch (Exception e) {
                log.error("handleWXRefundResult,refundInfo:{},result:{},e", JSON.toJSONString(request), JSON.toJSONString(result), e);
                status.setRollbackOnly();
                execute = false;
            }
            return execute;
        });
    }

    /**
     * 处理退款申请响应数据
     *
     * @param result 响应数据
     */
    private void handleWXRefundResult(RefundExecuteRequest request, RefundResult result) {
        Refund refund = request.getRefund();
        log.info("处理退款请求响应数据，result：{}", JSONObject.toJSONString(result));
        Refund updateRefund = new Refund();
        updateRefund.setId(refund.getId());
        updateRefund.setRefundId(result.getRefundId());
        updateRefund.setUserReceivedAccount(result.getUserReceivedAccount());
        updateRefund.setChannel(result.getChannel());
        updateRefund.setStatus(result.getStatus());
        updateRefund.setRefundStatus(RefundEnum.Status.IN_REFUND.getStatus());
        refundMapper.updateByPrimaryKeySelective(updateRefund);

        // 更新售后单状态
        if (refund.getAfterSaleId() != null) {
            OrderAfterSaleUpdateReq update = new OrderAfterSaleUpdateReq();
            update.setId(refund.getAfterSaleId());
            update.setStatus(com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum.REFUNDING.getValue());
            RpcResultUtil.handle(orderAfterSaleCommandProvider.updateById(update));
        }
    }

    private WechatRefundExecuteRequest buildExecuteRequest(RefundExecuteRequest request) {
        Refund refund = request.getRefund();
        Payment payment = request.getPayment();
        String refundNo = refund.getRefundNo();

        WechatRefundExecuteRequest wechatRefundExecuteRequest = new WechatRefundExecuteRequest();
        wechatRefundExecuteRequest.setRefund(refund);
        wechatRefundExecuteRequest.setOut_trade_no(payment.getPaymentNo());
        wechatRefundExecuteRequest.setOut_refund_no(refundNo);
        wechatRefundExecuteRequest.setNotify_url(notifyDomain + "/pay-notify/wx-direct");

        Amount amount = new Amount();
        long totalCent = new Money(payment.getTotalPrice()).getCent();
        amount.setTotal((int) totalCent);
        long refundCent = new Money(refund.getRefundPrice()).getCent();
        amount.setRefund((int) refundCent);
        amount.setCurrency(Money.DEFAULT_CURRENCY_CODE);
        wechatRefundExecuteRequest.setAmount(amount);
        wechatRefundExecuteRequest.setSpMchid(payment.getSpMchid());
        wechatRefundExecuteRequest.setPayCertPath(request.getPayCertPath());

        return wechatRefundExecuteRequest;
    }

    @Override
    protected void onFailure(RefundExecuteRequest request, RefundExecuteResult result) {

    }

    @Override
    protected void onSuccess(RefundExecuteRequest request, RefundExecuteResult result) {

    }
}
