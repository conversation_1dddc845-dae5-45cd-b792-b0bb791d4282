package com.cosfo.mall.payment.template.nativepay;

import com.cosfo.mall.common.result.ResultDTOEnum;
import com.cosfo.mall.payment.model.request.PaymentRequest;
import com.cosfo.mall.payment.model.result.PaymentResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ZeroPricePayment extends NativePayment {

    @Override
    protected PaymentResult assemblyPaymentResult(PaymentRequest request, PaymentResult result) {
        super.assemblyPaymentResult(request, result);
        if (result.isSuccess()) {
            result.setCode(ResultDTOEnum.ZERO_ORDER_PAYMENT_SUCCESS);
        }
        return result;
    }
}
