package com.cosfo.mall.payment.template.nativepay;

import com.cosfo.mall.order.service.OrderAfterSaleService;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.model.request.RefundExecuteRequest;
import com.cosfo.mall.payment.model.result.RefundExecuteResult;
import com.cosfo.mall.tenant.service.TenantPrepaymentService;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: 账期退款
 * @author: George
 * @date: 2023-09-08
 **/
@Service
@Slf4j
public class BillRefund extends NativeRefund {

    @Resource
    private TenantPrepaymentService tenantPrepaymentService;
    @Resource
    private OrderAfterSaleService orderAfterSaleService;

    @Override
    protected RefundExecuteResult processRefund(RefundExecuteRequest request) {
        Refund refund = request.getRefund();
        OrderAfterSaleResp orderAfterSale = orderAfterSaleService.queryById(refund.getAfterSaleId());
        request.setOrderAfterSale(orderAfterSale);

        tenantPrepaymentService.dealThreePartiesOrderPrepaymentRefund(orderAfterSale);
        return RefundExecuteResult.builder().isSuccess(true).build();
    }
}
