package com.cosfo.mall.payment.template.nativepay;

import com.cosfo.mall.common.ErrorCodeEnum;
import com.cosfo.mall.common.context.MerchantStoreBalanceChangeRecordTypeEnum;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalance;
import com.cosfo.mall.merchant.model.po.MerchantStoreBalanceChangeRecord;
import com.cosfo.mall.merchant.service.MerchantStoreBalanceChangeRecordService;
import com.cosfo.mall.merchant.service.MerchantStoreBalanceService;
import com.cosfo.mall.order.service.OrderAfterSaleService;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.payment.model.request.RefundExecuteRequest;
import com.cosfo.mall.payment.model.result.RefundExecuteResult;
import com.cosfo.mall.tenant.service.TenantPrepaymentService;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.exception.ProviderException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: George
 * @date: 2025-04-22
 **/
@Slf4j
@Service
public class NonCashRefund extends NativeRefund {

    @Resource
    private MerchantStoreBalanceService merchantStoreBalanceService;
    @Resource
    private MerchantStoreBalanceChangeRecordService merchantStoreBalanceChangeRecordService;
    @Resource
    private TenantPrepaymentService tenantPrepaymentService;
    @Resource
    private PlatformTransactionManager transactionManager;
    @Resource
    private OrderAfterSaleService orderAfterSaleService;


    @Override
    protected RefundExecuteResult processRefund(RefundExecuteRequest request) {
        Refund refund = request.getRefund();
        OrderAfterSaleResp orderAfterSale = orderAfterSaleService.queryById(refund.getAfterSaleId());
        request.setOrderAfterSale(orderAfterSale);

        // 手动开启事务
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        TransactionStatus status = transactionManager.getTransaction(def);
        try {
            // 非现金退款
            processNonCashRefund(request);
            // 预付退款
            processThreePartiesOrderPrepaymentRefund(request);
            // 提交事务
            transactionManager.commit(status);
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw e;
        }
        return RefundExecuteResult.builder().isSuccess(true).build();
    }

    /**
     * 处理非现金账户的退款
     * 1.售后审核退款指定账户
     * 2.无需审核 则退款到第一个账户
     *
     * @param request
     */
    private void processNonCashRefund(RefundExecuteRequest request) {
        Refund refund = request.getRefund();
        Long tenantId = refund.getTenantId();
        BigDecimal refundPrice = refund.getRefundPrice();
        OrderAfterSaleResp orderAfterSale = request.getOrderAfterSale();
        Long storeId = orderAfterSale.getStoreId();
        String orderAfterSaleNo = orderAfterSale.getAfterSaleOrderNo();

        // 获取并排序非现金账户
        List<MerchantStoreBalance> merchantStoreBalances = merchantStoreBalanceService.queryNonCashAccountByStoreId(tenantId, storeId);
        if (CollectionUtils.isEmpty(merchantStoreBalances)) {
            throw new ProviderException("未查询到非现金账户，无法退款，请关注");
        }

        // 按照资金账户id排序
        merchantStoreBalances.sort(Comparator.comparing(MerchantStoreBalance::getFundAccountId));

        // 售后可以指定退款账户，如果不指定则退款到第一个账户
        Long balanceId = orderAfterSale.getBalanceId();

        // 直接从已排序的列表中查找，避免二次查询
        MerchantStoreBalance targetBalance = null;
        if (balanceId != null) {
            targetBalance = merchantStoreBalances.stream()
                    .filter(balance -> balance.getId().equals(balanceId))
                    .findFirst()
                    .orElse(null);
        }

        // 如果没有找到指定账户，使用第一个账户
        if (targetBalance == null) {
            targetBalance = merchantStoreBalances.get(0);
        }

        // 执行账户余额增加操作
        int result = merchantStoreBalanceService.increaseBalance(targetBalance.getId(), refundPrice);
        if (result != 1) {
            throw new ProviderException("非现金账户售后返还金额失败，请关注");
        }
        log.info("售后单号：{}，非现金账户售后返还金额成功，账户id：{}，金额：{}", orderAfterSaleNo, targetBalance.getId(), refundPrice);

        // 获取更新后的余额，确保记录准确的余额变更
        MerchantStoreBalance updatedBalance = merchantStoreBalanceService.queryById(targetBalance.getId());
        BigDecimal afterChangeBalance = updatedBalance != null ?
                updatedBalance.getBalance() : targetBalance.getBalance().add(refundPrice);

        // 创建并保存余额变更记录
        MerchantStoreBalanceChangeRecord record = new MerchantStoreBalanceChangeRecord();
        record.setTenantId(tenantId);
        record.setStoreId(storeId);
        record.setChangeBalance(refundPrice);
        record.setAfterChangeBalance(afterChangeBalance);
        record.setAssociatedOrderNo(orderAfterSaleNo);
        record.setType(MerchantStoreBalanceChangeRecordTypeEnum.REFUND.getType());
        record.setAccountType(targetBalance.getAccountType());
        record.setFundAccountId(targetBalance.getFundAccountId());
        merchantStoreBalanceChangeRecordService.batchInsertChangeRecord(Collections.singletonList(record));
    }

    /**
     * 处理预付的退款
     *
     * @param request
     */
    private void processThreePartiesOrderPrepaymentRefund(RefundExecuteRequest request) {
        OrderAfterSaleResp orderAfterSale = request.getOrderAfterSale();
        tenantPrepaymentService.dealThreePartiesOrderPrepaymentRefund(orderAfterSale);
    }
}
