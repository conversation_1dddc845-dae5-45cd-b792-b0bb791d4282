package com.cosfo.mall.payment.model.dto;

import com.cosfo.mall.order.model.po.Order;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentItem;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/23  11:02
 */
@Data
public class PaymentDTO extends Payment {
    /**
     * 支付订单明细
     */
    private List<PaymentItem> paymentItemList;
    /**
     * 支付类型 1、线上支付 2、账期 3、余额支付
     */
    private Integer payType;

    /**
     * 支付渠道 0、微信 1、汇付
     */
    private Integer onlinePayChannel;

    /**
     * 分账开关
     * @see com.cosfo.mall.common.constants.ProfitSharingSwitchEnum
     */
    private Integer profitSharingSwitch;

    /**
     * 订单
     */
    private List<Order> orders;

    /**
     * 是否是組合包
     */
    private Boolean combine;

    /**
     * 用于汇付或者微信支付的支付单据的description，参考：
     * https://paas.huifu.com/partners/api/#/smzf/api_jhzs
     * https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_1.shtml
     */
    private String paymentDesc;

    /**
     * H5请求
     */
    private Boolean H5Request;
}
