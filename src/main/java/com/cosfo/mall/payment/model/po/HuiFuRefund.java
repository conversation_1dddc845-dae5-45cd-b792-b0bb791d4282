package com.cosfo.mall.payment.model.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 退款记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-27
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("hui_fu_refund")
public class HuiFuRefund implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * primary key
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 退款id
     */
    @TableField("refund_id")
    private Long refundId;

    /**
     * create time
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * update time
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 业务响应码
     */
    @TableField("resp_code")
    private String respCode;

    /**
     * 业务响应信息
     */
    @TableField("resp_desc")
    private String respDesc;

    /**
     * 交易状态 P：处理中、S：成功、F：失败；示例值：S
     */
    @TableField("trans_stat")
    private String transStat;

    /**
     * 全局流水号
     */
    @TableField("hf_seq_id")
    private String hfSeqId;

    /**
     * 请求日期
     */
    @TableField("req_date")
    private String reqDate;

    /**
     * 请求流水号
     */
    @TableField("req_seq_id")
    private String reqSeqId;

    /**
     * 商户号
     */
    @TableField("huifu_id")
    private String huifuId;

    /**
     * 原交易请求流水号
     */
    @TableField("org_req_seq_id")
    private String orgReqSeqId;

    /**
     * 是否垫资退款
     */
    @TableField("loan_flag")
    private String loanFlag;

    /**
     * 垫资承担者
     */
    @TableField("loan_undertaker")
    private String loanUndertaker;

    /**
     * 垫资账户类型
     */
    @TableField("loan_acct_type")
    private String loanAcctType;

    /**
     * 待确认总金额
     */
    @TableField("unconfirm_amt")
    private String unconfirmAmt;

    /**
     * 已确认总金额
     */
    @TableField("confirmed_amt")
    private String confirmedAmt;

    /**
     * 支付交易业务请求时间
     */
    @TableField("org_req_date")
    private String orgReqDate;

    /**
     * 支付交易汇付全局流水号
     */
    @TableField("org_hf_seq_id")
    private String orgHfSeqId;

    /**
     * 手续费
     */
    @TableField("fee_amount")
    private String feeAmount;


}
