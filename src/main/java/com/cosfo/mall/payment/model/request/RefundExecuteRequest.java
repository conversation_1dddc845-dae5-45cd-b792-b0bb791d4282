package com.cosfo.mall.payment.model.request;

import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import lombok.Data;

/**
 * @description: 退款执行对象
 * @author: <PERSON>
 * @date: 2023-09-08
 **/
@Data
public class RefundExecuteRequest {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 售后信息
     */
    private OrderAfterSaleResp orderAfterSale;

    /**
     * 退款单信息
     */
    private Refund refund;

    /**
     * 支付单信息
     */
    private Payment payment;

    /**
     * 汇付支付单信息
     */
    private HuiFuPayment huiFuPayment;

    /**
     * 支付证书
     */
    private transient String payCertPath;
}
