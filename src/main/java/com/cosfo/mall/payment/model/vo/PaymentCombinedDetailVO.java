package com.cosfo.mall.payment.model.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 组合支付详情
 * @author: <PERSON>
 * @date: 2025-04-21
 **/
@Data
public class PaymentCombinedDetailVO {

    /**
     * 支付方式 1.微信支付 2.账期支付 3.余额支付 4.支付宝支付 5.无需支付 6.线下支付 7.非现金支付 8.组合支付
     *
     * @see com.cosfo.mall.common.constants.PayTypeEnum
     */
    private Integer payType;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;
}
