package com.cosfo.mall.payment.model.dto;

import com.cosfo.mall.common.constants.RefundSourceEnum;
import com.cosfo.mall.payment.model.po.Refund;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/5/25  11:47
 */
@Data
public class RefundDTO extends Refund {
    /**
     * 原订单编号
     */
    private Long orderId;

    /**
     * 逆向分账退款明细
     */
    private List<RefundAcctSplitDetailDTO> refundAcctSplitDetailDTOList;

    /**
     * 退款重试
     */
    private Integer warningNum;

    /**
     * 标注来源
     */
    private RefundSourceEnum source = RefundSourceEnum.DEFAULT;
}
