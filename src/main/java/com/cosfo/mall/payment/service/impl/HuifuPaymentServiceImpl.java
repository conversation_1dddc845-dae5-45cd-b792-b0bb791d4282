package com.cosfo.mall.payment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cosfo.mall.order.mapper.HuiFuPaymentMapper;
import com.cosfo.mall.order.model.po.HuiFuPayment;
import com.cosfo.mall.payment.service.HuifuPaymentService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Author: fansongsong
 * @Date: 2023-11-17
 * @Description:
 */
@Service
public class HuifuPaymentServiceImpl  implements HuifuPaymentService {

    @Resource
    private HuiFuPaymentMapper huiFuPaymentMapper;

    @Override
    public int updateByHfSeqId(HuiFuPayment huiFuPayment, String hfSeqId) {
        return huiFuPaymentMapper.update(huiFuPayment, new LambdaQueryWrapper<HuiFuPayment>().eq(HuiFuPayment::getHfSeqId, hfSeqId));
    }

    @Override
    public int updateByPaymentId(HuiFuPayment huiFuPayment, Long paymentId) {
        return huiFuPaymentMapper.update(huiFuPayment, new LambdaQueryWrapper<HuiFuPayment>().eq(HuiFuPayment::getPaymentId, paymentId));
    }

    @Override
    public HuiFuPayment selectByReqSeqId(String reqSeqId, String huifuId) {
        return huiFuPaymentMapper.selectOne(new LambdaQueryWrapper<HuiFuPayment>()
                .eq(HuiFuPayment::getReqSeqId, reqSeqId)
                .eq(HuiFuPayment::getHuifuId, huifuId)
                .orderByDesc(HuiFuPayment::getId).last("limit 1"));
    }

    @Override
    public HuiFuPayment selectByPaymentId(Long paymentId) {
        return huiFuPaymentMapper.selectOne(new LambdaQueryWrapper<HuiFuPayment>().eq(HuiFuPayment::getPaymentId, paymentId));
    }
}
