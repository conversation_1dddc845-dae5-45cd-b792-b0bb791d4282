package com.cosfo.mall.payment.service;

import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.payment.model.dto.PayResultDTO;
import com.cosfo.mall.payment.model.dto.PaymentDTO;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/7/13
 */
public interface PaymentV2Service {

//    /**
//     * 订单支付
//     *
//     * @param paymentDTO
//     * @param loginContextInfoDTO
//     * @return
//     */
//    PayResultDTO orderPay(PaymentDTO paymentDTO, LoginContextInfoDTO loginContextInfoDTO);
}
