package com.cosfo.mall.payment.service;

import com.cosfo.mall.common.constants.TradeTypeEnum;

import java.util.List;

/**
 * @description: 支付-订单服务接口
 * @author: <PERSON>
 * @date: 2024-08-22
 **/
public interface PaymentOrderService {

    /**
     * 订单支付成功
     *
     * @param orderIds
     */
    void orderPaySuccess(List<Long> orderIds);

    /**
     * 订单补偿
     *
     * @param orderIds
     */
    void orderCompensation(List<Long> orderIds);
}
