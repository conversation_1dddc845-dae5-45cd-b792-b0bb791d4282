package com.cosfo.mall.payment.service.impl;

import com.cosfo.mall.bill.service.BillSettlementService;
import com.cosfo.mall.bill.service.ProfitSharingBusinessService;
import com.cosfo.mall.common.context.PayNotifyBizType;
import com.cosfo.mall.payment.model.bo.PayNotifyBO;
import com.cosfo.mall.payment.service.AbstractPaymentNotifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 支付回调通知结算相关处理接口
 * @author: George
 * @date: 2024-08-19
 **/
@Slf4j
@Service
public class PaymentSettlementNotifyServiceImpl extends AbstractPaymentNotifyService {

    @Resource
    private BillSettlementService billSettlementService;
    @Resource
    private ProfitSharingBusinessService profitSharingBusinessService;

    @Override
    protected String getBizType() {
        return PayNotifyBizType.SETTLEMENT.getBizType();
    }

    @Override
    protected void doBizNotify(PayNotifyBO payNotifyBO) {
        // 生成结算相关信息
        Long tenantId = payNotifyBO.getTenantId();
        List<Long> orderIds = payNotifyBO.getOrderIds();
        billSettlementService.generateSettlements(tenantId, orderIds);

        // 如果是实时分账，立马进行分账
        try {
            profitSharingBusinessService.profitSharingByOrderIdsRightNow(tenantId, orderIds);
        } catch (Exception e) {
            log.error("实时分账异常,tenantId:{},orderIds:{}", tenantId, orderIds, e);
        }
    }
}
