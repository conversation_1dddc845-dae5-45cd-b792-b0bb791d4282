package com.cosfo.mall.payment.service;

import com.cosfo.mall.common.context.PaymentEnum;
import com.cosfo.mall.payment.manager.PaymentNotifyLockManager;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.bo.PayNotifyBO;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentItem;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @description: 支付回调通知抽象类
 * @author: George
 * @date: 2024-08-19
 **/
@Slf4j
@Service
public abstract class AbstractPaymentNotifyService implements PaymentNotifyService {

    @Resource
    private PaymentNotifyLockManager paymentNotifyLockManager;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private PaymentItemMapper paymentItemMapper;

    @Override
    public void notify(String paymentNo) {
        // 加锁保证幂等性
        RLock lock = paymentNotifyLockManager.lock(paymentNo, getBizType());
        try {
            doNotify(paymentNo);
        } finally {
            lock.unlock();
        }
    }

    protected abstract String getBizType();

    protected void doNotify(String paymentNo) {
        log.info("开始通知，支付单号：{}", paymentNo);
        Payment payment = paymentMapper.selectByPaymentNo(paymentNo);
        if (payment == null) {
            log.error("支付单不存在，支付单号：{}", paymentNo);
            return;
        }
        List<PaymentItem> paymentItems = paymentItemMapper.selectByPaymentNo(paymentNo);
        if (paymentItems == null || paymentItems.isEmpty()) {
            log.error("支付单明细不存在，支付单号：{}", paymentNo);
            return;
        }

        if (!Objects.equals(payment.getStatus(), PaymentEnum.Status.SUCCESS.getCode())) {
            log.error("支付单状态不是支付成功，支付单号：{}, 处理流程结束", paymentNo);
            return;
        }

        List<Long> orderIds = paymentItems.stream().map(PaymentItem::getOrderId).collect(Collectors.toList());
        PayNotifyBO payNotifyBO = PayNotifyBO.builder()
                .tenantId(payment.getTenantId())
                .paymentId(payment.getId())
                .paymentNo(paymentNo)
                .orderIds(orderIds)
                .tradeType (payment.getTradeType ())
                .build();
        // 具体业务处理
        doBizNotify(payNotifyBO);
    }

    protected abstract void doBizNotify(PayNotifyBO payNotifyBO);
}
