package com.cosfo.mall.facade;

import com.cosfo.mall.openapi.model.dto.OrderDeliveryCompletedDTO;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.open.client.provider.event.BizEventPushProvider;
import net.xianmu.open.client.req.BizEventPushRequest;
import net.xianmu.open.client.resq.BizEventPushResponse;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.security.ProviderException;

/**
 * @Author: fansongsong
 * @Date: 2023-10-31
 * @Description:
 */
@Component
public class BizEventPushFacade {

    @DubboReference
    private BizEventPushProvider bizEventPushProvider;

    /**
     * 配送完成的回调事件类型
     */
    public static final String ORDER_DELIVERY_FINISH_NOTICE = "order.delivery.finish.notice";


    public BizEventPushResponse pushBizEvent(BizEventPushRequest bizEventPushRequest) {
        DubboResponse<BizEventPushResponse> response = bizEventPushProvider.pushBizEvent(bizEventPushRequest);
        if (!response.isSuccess()) {
            throw new ProviderException("推送业务事件失败");
        }
        return response.getData();
    }

    /**
     * 推送订单配送完成事件
     * @param tenantId
     * @param orderNo
     * @param orderDeliveryCompletedDTO
     * @return
     */
    public BizEventPushResponse pushOrderDeliveryFinish(Long tenantId, String orderNo,
                                                        OrderDeliveryCompletedDTO orderDeliveryCompletedDTO) {
        BizEventPushRequest bizEventPushRequest = new BizEventPushRequest();
        bizEventPushRequest.setEventType(ORDER_DELIVERY_FINISH_NOTICE);
        bizEventPushRequest.setAccountId(tenantId);
        bizEventPushRequest.setBizId(orderNo);
        bizEventPushRequest.setData(orderDeliveryCompletedDTO);
        return pushBizEvent(bizEventPushRequest);
    }


}
