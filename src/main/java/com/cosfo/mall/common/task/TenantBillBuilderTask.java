package com.cosfo.mall.common.task;


import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.mapper.RefundMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.Refund;
import com.cosfo.mall.tenant.service.TenantBillService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

@Component
@Slf4j
public class TenantBillBuilderTask extends XianMuJavaProcessorV2 {

    @Resource
    private TenantBillService tenantBillService;
    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private RefundMapper refundMapper;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime past12H = now.minusHours(12);

        buildTenantBill(past12H, now);

        return new ProcessResult(true);
    }

     private void buildTenantBill(LocalDateTime startTime, LocalDateTime endTime) {
         log.info("------开始插入账单的全量链路, startTime: {}, endTime: {}", startTime, endTime);

         List<Payment> payments = paymentMapper.querySuccessPayments(startTime.toString(), endTime.toString());
         payments.forEach(payment -> tenantBillService.createTenantBillForPayment(payment.getId()));

         payments = paymentMapper.queryDuplicatePayments(startTime.toString(), endTime.toString());
         payments.forEach(payment -> tenantBillService.createTenantBillForPayment(payment.getId()));

         List<Refund> refunds = refundMapper.querySuccessRefund(startTime.toString(), endTime.toString());
         refunds.forEach(refund -> tenantBillService.createTenantBillForRefund(refund.getId()));

         log.info("------插入账单全量链路结束");
     }
}
