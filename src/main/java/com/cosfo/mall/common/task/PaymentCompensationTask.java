package com.cosfo.mall.common.task;

import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.mall.bill.service.BillSettlementService;
import com.cosfo.mall.payment.mapper.PaymentItemMapper;
import com.cosfo.mall.payment.mapper.PaymentMapper;
import com.cosfo.mall.payment.model.po.Payment;
import com.cosfo.mall.payment.model.po.PaymentItem;
import com.cosfo.mall.payment.service.PaymentOrderService;
import com.cosfo.mall.tenant.service.TenantBillService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 支付补偿任务
 * @author: George
 * @date: 2024-08-21
 **/
@Component
@Slf4j
public class PaymentCompensationTask extends XianMuJavaProcessorV2 {

    @Resource
    private PaymentMapper paymentMapper;
    @Resource
    private TenantBillService tenantBillService;
    @Resource
    private PaymentItemMapper paymentItemMapper;
    @Resource
    private BillSettlementService billSettlementService;
    @Resource
    private PaymentOrderService paymentOrderService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        Pair<String, String> executeTimeRange = getExecuteTimeRange(context);
        String startTime = executeTimeRange.getKey();
        String endTime = executeTimeRange.getValue();
        log.info("起止时间:{}-{} 支付补偿任务开始执行...", startTime, endTime);
        // 查询符合条件的支付单
        List<Payment> payments = paymentMapper.querySuccessPaymentsByTime(startTime, endTime);
        if (CollectionUtils.isEmpty(payments)) {
            log.info("起止时间:{}-{} 未查询到需要补偿的支付记录，支付补偿任务执行完毕...", startTime, endTime);
            return new ProcessResult(true);
        }

        for (Payment payment : payments) {
            singlePaymentCompensation(payment);
        }

        log.info("起止时间:{}-{} 支付补偿任务执行结束...", startTime, endTime);
        return new ProcessResult(true);
    }

    private void singlePaymentCompensation(Payment payment) {
        log.info("开始补偿支付单{}", payment.getPaymentNo());
        Long id = payment.getId();
        Long tenantId = payment.getTenantId();
        List<PaymentItem> paymentItems = paymentItemMapper.selectByPaymentId(id);
        List<Long> orderIds = paymentItems.stream().map(PaymentItem::getOrderId).collect(Collectors.toList());

        // 结算相关（费用明细、分账快照补偿）
        billSettlementService.generateSettlements(tenantId, orderIds);
        // 交易流水补偿
        tenantBillService.createTenantBillForPayment(payment.getId());
        // 订单状态补偿（如果还是待支付则调用订单中心）
        paymentOrderService.orderCompensation(orderIds);

        log.info("支付单{}补偿结束", payment.getPaymentNo());
    }

    private Pair<String, String> getExecuteTimeRange(XmJobInput context) {
        // 默认为过去一分钟整的时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:00");
        LocalDateTime now = LocalDateTime.now();
        String endTime = now.minusMinutes(1).format(formatter);
        String startTime = now.minusMinutes(2).format(formatter);
        String instanceParameters = context.getInstanceParameters();
        if (!StringUtils.isBlank(instanceParameters)) {
            JSONObject jsonObject = JSONObject.parseObject(instanceParameters);
            startTime = jsonObject.getString("startTime");
            endTime = jsonObject.getString("endTime");
        }

        return Pair.of(startTime, endTime);
    }


}
