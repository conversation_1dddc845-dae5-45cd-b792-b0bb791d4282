//package com.cosfo.mall.common.task;
//
//import com.alibaba.fastjson.JSON;
//import com.alibaba.schedulerx.worker.processor.ProcessResult;
//import com.cosfo.mall.tenant.service.TenantBillOfflineBuilder;
//import java.util.Set;
//import javax.annotation.Resource;
//import lombok.Data;
//import lombok.experimental.Accessors;
//import net.xianmu.task.process.XianMuJavaProcessorV2;
//import net.xianmu.task.vo.input.XmJobInput;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Component;
//
//@Component
//public class TenantBillOfflineBuilderTask extends XianMuJavaProcessorV2 {
//
//    @Resource
//    private TenantBillOfflineBuilder tenantBillOfflineBuilder;
//
//    @Override
//    public ProcessResult processResult(XmJobInput context) throws Exception {
//        String params = context.getInstanceParameters();
//        if (StringUtils.isEmpty(params)) {
//            params = context.getJobParameters();
//        }
//
//        if (StringUtils.isEmpty(params)) {
//            return new ProcessResult(false, "参数为空");
//        }
//        Param param = JSON.parseObject(params, Param.class);
//
//        tenantBillOfflineBuilder.initTenantBill(param.getTenantIds(), param.startDate, param.endDate);
//
//        return new ProcessResult(true, params);
//    }
//
//    @Data
//    @Accessors(chain = true)
//    public static class Param {
//
//        private Set<Long> tenantIds;
//
//        private String startDate;
//
//        private String endDate;
//    }
//}
