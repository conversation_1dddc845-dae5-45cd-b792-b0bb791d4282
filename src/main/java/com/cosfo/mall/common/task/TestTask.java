package com.cosfo.mall.common.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/10
 */
@Slf4j
@Component
public class TestTask extends XianMuJavaProcessorV2 {

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        log.info("测试接入调度任务成！！！！！！！！！！！！！！！！！");
        return new ProcessResult(true);
    }
}
