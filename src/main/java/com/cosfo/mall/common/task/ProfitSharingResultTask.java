package com.cosfo.mall.common.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.mall.bill.service.BillProfitSharingService;
import com.cosfo.mall.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2022/10/20
 */
@Component
@Slf4j
public class ProfitSharingResultTask extends XianMuJavaProcessorV2 {
    @Resource
    private BillProfitSharingService billProfitSharingService;

    @Override
    public ProcessResult processResult(XmJobInput jobContext) throws Exception {
        log.info("分账结果查询任务开始------");

        // 解析任务参数
        String jobParameters = jobContext.getJobParameters();
        String instanceParameters = jobContext.getInstanceParameters();

        // 获取时间范围参数
        TimeRange timeRange = parseTimeRange(jobParameters, instanceParameters);

        // 统一调用一个方法，时间参数可以为null
        billProfitSharingService.queryProfitSharingResult(timeRange.startTime, timeRange.endTime);

        log.info("分账结果查询任务结束------");
        return new ProcessResult(true);
    }

    /**
     * 解析时间范围参数
     */
    private TimeRange parseTimeRange(String jobParameters, String instanceParameters) {
        TimeRange timeRange = new TimeRange();

        // 优先使用实例参数
        if (StringUtils.isNotBlank(instanceParameters)) {
            try {
                TaskParams params = JSON.parseObject(instanceParameters, TaskParams.class);
                if (params != null) {
                    timeRange.startTime = params.getStartTime();
                    timeRange.endTime = params.getEndTime();
                }
            } catch (Exception e) {
                log.warn("解析实例参数失败：{}", instanceParameters, e);
            }
        }

        // 如果实例参数没有时间范围，尝试解析作业参数
        if (!timeRange.hasTimeRange() && StringUtils.isNotBlank(jobParameters)) {
            try {
                TaskParams params = JSON.parseObject(jobParameters, TaskParams.class);
                if (params != null) {
                    timeRange.startTime = params.getStartTime();
                    timeRange.endTime = params.getEndTime();
                }
            } catch (Exception e) {
                log.warn("解析作业参数失败：{}", jobParameters, e);
            }
        }

        // 如果都没有指定时间范围，保持为null，表示不限制时间
        if (!timeRange.hasTimeRange()) {
            log.info("未指定时间范围，将查询所有符合状态条件的分账订单");
        }

        return timeRange;
    }

    /**
     * 任务参数
     */
    private static class TaskParams {
        private String startTime;
        private String endTime;

        public String getStartTime() {
            return startTime;
        }

        public void setStartTime(String startTime) {
            this.startTime = startTime;
        }

        public String getEndTime() {
            return endTime;
        }

        public void setEndTime(String endTime) {
            this.endTime = endTime;
        }
    }

    /**
     * 时间范围
     */
    private static class TimeRange {
        private String startTime;
        private String endTime;

        public boolean hasTimeRange() {
            return StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime);
        }
    }
}
