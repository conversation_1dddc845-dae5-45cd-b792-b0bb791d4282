package com.cosfo.mall.common.task;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.RefundSourceEnum;
import com.cosfo.mall.payment.model.dto.RefundDTO;
import com.cosfo.mall.payment.service.RefundService;
import com.cosfo.ordercenter.client.common.OrderAfterSaleServiceTypeEnum;
import com.cosfo.ordercenter.client.common.OrderAfterSaleStatusEnum;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSalePageQueryReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleWithOrderResp;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.apache.commons.collections.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Author: fansongsong
 * @Date: 2023-08-24
 * @Description: 定时任务兜底去退款
 */
@Component
@Slf4j
public class AutoCreateRefundTask extends XianMuJavaProcessorV2 {

    @Resource
    private RefundService refundService;

    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;

    /**
     * 开始时间距离当前时间的天数
     */
    private static final String START_BEFORE_DAY = "startBeforeDay";

    /**
     * 结束时间距离当前时间的天数
     */
    private static final String END_BEFORE_DAY = "endBeforeDay";

    /**
     * 单次查询量
     */
    private static final String LIMIT_SIZE = "limitSize";

    @Override
    public ProcessResult processResult(XmJobInput context) {
        log.info("退款中订单自动生成退款单开始------");
        Integer startBeforeDay = NumberConstant.THREE;
        Integer endBeforeDay = NumberConstant.ONE;
        Integer limitSize = NumberConstant.HUNDRED;
        try {
            log.info("executeRetry jobParameters:{}", context.getJobParameters());
            JSONObject jsonObject = JSONObject.parseObject(context.getJobParameters());
            if (jsonObject.containsKey(START_BEFORE_DAY)) {
                startBeforeDay = jsonObject.getInteger(START_BEFORE_DAY);
            }
            if (jsonObject.containsKey(END_BEFORE_DAY)) {
                endBeforeDay = jsonObject.getInteger(END_BEFORE_DAY);
            }
            if (jsonObject.containsKey(LIMIT_SIZE)) {
                limitSize = jsonObject.getInteger(LIMIT_SIZE);
            }
        } catch (Exception e) {
            log.error("executeRetry 转换失败 jobParameters:{}", context.getJobParameters());
        }

        // 近三天内的数据进行轮询处理
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime endTime = now.minusDays(endBeforeDay);
        LocalDateTime startTime = now.minusDays(startBeforeDay);

        // 查询退款中数据
        OrderAfterSalePageQueryReq orderAfterSalePageQueryReq = new OrderAfterSalePageQueryReq();
        orderAfterSalePageQueryReq.setStartTime(startTime);
        orderAfterSalePageQueryReq.setEndTime(endTime);
        orderAfterSalePageQueryReq.setStatusList(Collections.singletonList(OrderAfterSaleStatusEnum.REFUNDING.getValue()));
        orderAfterSalePageQueryReq.setPageNum(NumberConstant.ONE);
        orderAfterSalePageQueryReq.setPageSize(limitSize);
        PageInfo<OrderAfterSaleWithOrderResp> pageInfo = orderAfterSaleQueryProvider.queryPage(orderAfterSalePageQueryReq).getData();
        if (Objects.isNull(pageInfo) || CollectionUtils.isEmpty(pageInfo.getList())) {
            log.info("退款中订单自动生成退款单结束,扫描出0条数据");
            return new ProcessResult(true);
        }

        List<OrderAfterSaleWithOrderResp> orderAfterSaleDTOS = pageInfo.getList();
        for (OrderAfterSaleWithOrderResp orderAfterSale : orderAfterSaleDTOS) {
            // 校验售后单类型
            Integer serviceType = orderAfterSale.getServiceType();
            if (OrderAfterSaleServiceTypeEnum.EXCHANGE.getValue().equals(serviceType) ||
                    OrderAfterSaleServiceTypeEnum.RESEND.getValue().equals(serviceType)) {
                log.info("换货、补发类型售后单，无需执行退款 orderAfterSaleId:{}", orderAfterSale.getId());
                continue;
            }

            RefundDTO refundDTO = new RefundDTO();
            refundDTO.setOrderId(orderAfterSale.getOrderId());
            refundDTO.setTenantId(orderAfterSale.getTenantId());
            refundDTO.setAfterSaleId(orderAfterSale.getId());
            refundDTO.setRefundPrice(orderAfterSale.getTotalPrice());
            refundDTO.setSource(RefundSourceEnum.TASK);
            refundService.refundRequest(refundDTO);
        }

        log.info("退款中订单自动生成退款单结束，扫描出{}条数据------", orderAfterSaleDTOS.size());
        return new ProcessResult(true);
    }

}
