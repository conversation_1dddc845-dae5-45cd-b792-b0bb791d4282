package com.cosfo.mall.common.listener;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.MqGroupConstant;
import com.cosfo.mall.common.constant.NumberConstant;
import com.cosfo.mall.common.constants.OrderEnums;
import com.cosfo.mall.common.context.DeliveryStateEnum;
import com.cosfo.ordercenter.client.common.util.RpcResultUtil;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleCommandProvider;
import com.cosfo.ordercenter.client.provider.OrderAfterSaleQueryProvider;
import com.cosfo.ordercenter.client.provider.OrderQueryProvider;
import com.cosfo.ordercenter.client.req.OrderAfterSaleProcessFinishReq;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;
import com.cosfo.ordercenter.client.resp.order.OrderResp;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessage;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessageDetail;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 自营仓补发售后， 只处理自营仓订单售后单
 * ‒ 补发出库成功消息
 */
@Slf4j
@Component
@MqListener(topic = "topic_ofc_common_task_finish",
        consumerGroup = MqGroupConstant.GID_OFC_TO_COSFO_RESENDSUCCESSAFTERSALE,
        tag = "tag_ofc_outbound_reissue"
)
public class OFCResendSuccess4AfterSaleListener extends AbstractMqListener<CommonFulfillmentFinishMessage> {

    @DubboReference
    private OrderQueryProvider orderQueryProvider;
    @DubboReference
    private OrderAfterSaleQueryProvider orderAfterSaleQueryProvider;
    @DubboReference
    private OrderAfterSaleCommandProvider orderAfterSaleCommandProvider;

    @Override
    public void process(CommonFulfillmentFinishMessage msg) {
        log.info("rocketmq 收到OFC消息自营仓补发售后，消息内容：{}", JSONObject.toJSONString(msg));
        //补发成功
        String sourceOrderNo = msg.getSourceOrderNo();
        if (StringUtil.isEmpty(sourceOrderNo)) {
            return;
        }

        List<OrderAfterSaleResp> afterSaleDTOList = RpcResultUtil.handle(orderAfterSaleQueryProvider.queryByNos(Lists.newArrayList(sourceOrderNo)));

        if (CollectionUtil.isEmpty(afterSaleDTOList)) {
            log.error("未查询到该售后单:{}, msg={}", sourceOrderNo, JSON.toJSONString(msg));
            return;
        }

        OrderAfterSaleResp afterSaleDTO = afterSaleDTOList.get(0);

        OrderResp orderDTO = RpcResultUtil.handle(orderQueryProvider.queryById(afterSaleDTO.getOrderId()));
        // 非自营仓订单，不处理
        if (!OrderEnums.WarehouseTypeEnum.SELF_SUPPLY.getCode().equals(orderDTO.getWarehouseType())) {
            log.info("该售后单不是自营仓订单，不处理:{}, msg={}", sourceOrderNo, JSON.toJSONString(msg));
            return;
        }

        List<CommonFulfillmentFinishMessageDetail> itemList = msg.getItemList();
        if (CollectionUtil.isEmpty(itemList)) {
            return;
        }

        OrderAfterSaleProcessFinishReq finishReq = new OrderAfterSaleProcessFinishReq();
        finishReq.setShortCount(itemList.get(NumberConstant.ZERO).getShortQuantity());
        finishReq.setState(DeliveryStateEnum.NORMAL.getState());
        finishReq.setOrderAfterSaleNo(sourceOrderNo);
        RpcResultUtil.handle(orderAfterSaleCommandProvider.processFinish(Lists.newArrayList(finishReq)));

        long endTime = System.currentTimeMillis();
        log.info("线程{}：MQ开始回调结束时间：{}ms", Thread.currentThread().getName(), endTime);
    }
}
