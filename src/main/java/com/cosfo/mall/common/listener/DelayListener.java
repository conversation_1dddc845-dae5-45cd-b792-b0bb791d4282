package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.MQTopicConstant;
import com.cosfo.mall.common.constant.MqGroupConstant;
import com.cosfo.mall.common.constant.MqTagConstant;
import com.cosfo.mall.common.mq.model.DelayData;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 延时消息(暂未使用,注意修改Listener注解)
 * @createTime 2021年11月26日 14:45:00
 */
@Slf4j
@Component
@MqOrderlyListener(topic = MQTopicConstant.COSFO_MALL_DELAY_LIST,
        consumerGroup = MqGroupConstant.GID_COSFO_MALL_DELAY,
        tag = MqTagConstant.TAG_DEMO
)
public class DelayListener extends AbstractMqListener<DelayData> {

    @Override
    public void process(DelayData delayData) {
        log.info("延时消息rocketmq,升级mq扩展包版本 receive：{}", JSONObject.toJSONString(delayData));
        //处理消息
    }
}
