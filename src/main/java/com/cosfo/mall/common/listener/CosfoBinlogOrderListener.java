package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.common.constant.MQTopicConstant;
import com.cosfo.mall.common.context.binlog.DbTableName;
import com.cosfo.mall.common.mq.model.DtsModelBO;
import com.cosfo.mall.common.utils.binlog.DbTableDmlFactory;
import com.cosfo.mall.common.utils.binlog.DbTableDmlService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 只监听order表的状态变动
 * <AUTHOR>
 */
@Slf4j
@Component
@MqOrderlyListener(
        topic = MQTopicConstant.MYSQL_BINLOG_SAAS_ORDERLY,
        consumerGroup = "GID_saas_mall_order_cosfo_binlog",
        tag = DbTableName.COSFO_TABLE_ORDER,
        maxReconsumeTimes = 20)
public class CosfoBinlogOrderListener extends AbstractMqListener<DtsModelBO> {

    @Resource
    private DbTableDmlFactory dbTableDmlFactory;

    @Override
    public void process(DtsModelBO dtsModel) {
        log.info("rocketmq 收到消息,事件类型:[{}]，recordId/msg-key:[{}]， 表:[{}].[{}]",
                dtsModel.getType(), dtsModel.getMsgKey(), dtsModel.getDatabase(), dtsModel.getTable());
        DbTableDmlService creator = dbTableDmlFactory.creator(dtsModel.getTable());
        if (Objects.nonNull(creator)) {
            log.info("dtsModel=[{}]", JSON.toJSONString(dtsModel));
            creator.tableDml(dtsModel);
        } else {
            log.info("未在DbTableDmlFactory注册的table:[{}],请先注册后再做处理!", dtsModel.getTable());
        }
    }
}
