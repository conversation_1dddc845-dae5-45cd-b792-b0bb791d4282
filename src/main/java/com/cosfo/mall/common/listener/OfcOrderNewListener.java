package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSONObject;
import com.cosfo.mall.common.constant.MqGroupConstant;
import com.cosfo.mall.order.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.ofc.client.common.message.fulfillmentFinish.CommonFulfillmentFinishMessage;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 描述: 订单完成配送，ofc只针对三方仓的订单，发此消息
 * <AUTHOR>
 */
@Slf4j
@Component
@MqListener(topic = "topic_ofc_common_task_finish",
        consumerGroup = MqGroupConstant.GID_OFC_TO_COSFO_ORDER_NEW,
        tag = "tag_ofc_fulfillment_finish_order"
)
public class OfcOrderNewListener extends AbstractMqListener<CommonFulfillmentFinishMessage> {

    @Resource
    private OrderService orderService;

    @Override
    public void process(CommonFulfillmentFinishMessage commonFulfillmentFinishMessage) {
        log.info("rocketmq 收到OFC消息 tag_ofc_fulfillment_finish_order，消息内容：{}", JSONObject.toJSONString(commonFulfillmentFinishMessage));
        long startTime = System.currentTimeMillis();
        log.info("线程{}：MQ开始回调执行时间：{}ms", Thread.currentThread().getName(), startTime);

        orderService.finishedByMessage(commonFulfillmentFinishMessage);

        long endTime = System.currentTimeMillis();
        log.info("线程{}：MQ开始回调结束时间：{}ms", Thread.currentThread().getName(), endTime);
    }
}
