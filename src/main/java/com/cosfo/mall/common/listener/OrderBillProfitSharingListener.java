package com.cosfo.mall.common.listener;

import com.alibaba.fastjson.JSON;
import com.cosfo.mall.bill.model.dto.BillProfitSharingMessageDTO;
import com.cosfo.mall.bill.service.ProfitSharingBusinessService;
import com.cosfo.mall.common.constant.MQTopicConstant;
import com.cosfo.mall.common.constant.MqGroupConstant;
import com.cosfo.mall.common.constant.MqTagConstant;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.rocketmq.support.annotation.MqListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @description:
 * @author: George
 * @date: 2023-12-07
 **/
@Slf4j
@Component
@MqListener(topic = MQTopicConstant.TOPIC_BILL_PROFIT_SHARING,
        consumerGroup = MqGroupConstant.GID_BILL_PROFIT_SHARING,
        tag = MqTagConstant.ORDER_PROFIT_SHARING
)
public class OrderBillProfitSharingListener extends AbstractMqListener<BillProfitSharingMessageDTO> {

    @Resource
    private ProfitSharingBusinessService profitSharingBusinessService;

    @Override
    public void process(BillProfitSharingMessageDTO billProfitSharingMessageDTO) {
        log.info("rocketmq 收到订单分账消息，消息内容：{}", JSON.toJSONString(billProfitSharingMessageDTO));
        Long orderId = billProfitSharingMessageDTO.getOrderId();
        String profitSharingNo = billProfitSharingMessageDTO.getProfitSharingNo();
        try {
            profitSharingBusinessService.doProfitSharing(profitSharingNo);
        } catch (Exception e) {
            log.error("订单分账失败，订单id：{}", orderId, e);
        }
        log.info("订单分账消息处理完成，订单id：{}", orderId);
    }
}
