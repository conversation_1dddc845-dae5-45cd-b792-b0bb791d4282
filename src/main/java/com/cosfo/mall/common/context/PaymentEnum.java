package com.cosfo.mall.common.context;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2022/5/23  11:46
 */
public class PaymentEnum {
    @Getter
    @AllArgsConstructor
    public enum Status {
        /**
         * 0、待支付
         */
        WAITING(0, "待支付", "I", "WAITING", "INIT"),
        /**
         * 1、支付成功
         */
        SUCCESS(1, "支付成功", "S", "SUCCESS", "SUCCESS"),
        /**
         * 2、支付失败
         */
        FAIL(2, "支付失败", "F", "PAYERROR", "FAIL"),
        /**
         * 3、取消支付
         */
        CANCELED(3, "取消支付", "N", "CLOSED", "CLOSE"),
        /**
         * 4、处理中(锁定)
         */
        DEALING(4, "处理中(锁定)", "P", "NOTPAY", "DOING"),

        /**
         * 5、支付成功幂等状态
         */
        DUPLICATE_SUCCESS(5, "支付成功幂等状态", null, null, null),

        /**
         * 6、冻结中
         */
        FREEZE(6, "冻结中", null, null, null),
        ;
        private Integer code;
        private String desc;
        private String huifuStat;
        private String wechatStat;
        private String dinStat;

        /**
         * 根据汇付返回码确定状态
         *
         * @param huifuStat
         * @return
         */
        public static Status getStatusByHuifuStat(String huifuStat){
            for(Status status: Status.values()){
                if(status.getHuifuStat().equals(huifuStat)){
                    return status;
                }
            }

            return null;
        }

        /**
         * 根据微信返回码确定支付状态
         *
         * @param wechatStat
         * @return
         */
        public static Status getStatusByWechatStat(String wechatStat){
            for (Status status : Status.values()) {
                if (status.getWechatStat().equals(wechatStat)) {
                    return status;
                }
            }

            return null;
        }

        public static Status getStatusByDinStat(String dinStat) {
            for (Status status : Status.values()) {
                if (status.getDinStat().equals(dinStat)) {
                    return status;
                }
            }

            return null;
        }
    }


    @Getter
    @AllArgsConstructor
    public enum Switch {

        CLOSE(0, "关闭"),

        OPEN(1, "打开"),
        ;

        private Integer type;

        private String desc;
    }
}
