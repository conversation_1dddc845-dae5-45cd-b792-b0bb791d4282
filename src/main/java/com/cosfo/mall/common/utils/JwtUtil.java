package com.cosfo.mall.common.utils;

import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import net.xianmu.common.exception.BizException;

import java.util.HashMap;
import java.util.Map;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/6/27
 */
public class JwtUtil {

    private final static String key = "COSFO_MALL";


    public final static String PREFIX_FLAG = "h5mall_";

    /**
     * 根据租户生成token
     *
     * @param tenantId
     * @return
     */
    // 设置签名，使用jwtUtils生成token方法
    public static String createToken(Long tenantId){
        Map<String,Object> payload = new HashMap<String,Object>();
        //载荷
        payload.put("tenantId", tenantId);

        String token = JWTUtil.createToken(payload, key.getBytes());

        return token;
    }

    public static Long parseToken(String token){
        String substring = token.substring(PREFIX_FLAG.length());
        // 通过密钥验证是否token有效
        if(!JWTUtil.verify(substring, key.getBytes())){
            throw new BizException("非法token");
        }

        JWT jwt = JWTUtil.parseToken(substring);
        Long tenantId = Long.valueOf((Integer)jwt.getPayload("tenantId"));
        return tenantId;
    }
}
