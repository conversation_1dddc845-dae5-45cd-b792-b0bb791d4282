package com.cosfo.mall.stock.model.po;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * stock_record
 * <AUTHOR>
@Data
public class StockRecord implements Serializable {
    /**
     * 主键、自增
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * stock sku id
     */
    private Long stockSkuId;

    /**
     * 记录类型：0、下单 1、取消订单 2、售后
     */
    private Integer type;

    /**
     * 变更前数量
     */
    private Integer beforeAmount;

    /**
     * 变更数量
     */
    private Integer changeAmount;

    /**
     * 变更后数量
     */
    private Integer afterAmount;

    private LocalDateTime updateTime;

    private LocalDateTime createTime;

    /**
     * 记录编号
     */
    private String recordNo;

    private static final long serialVersionUID = 1L;
}