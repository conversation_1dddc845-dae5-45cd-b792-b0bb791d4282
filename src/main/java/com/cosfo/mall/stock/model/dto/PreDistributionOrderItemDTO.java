package com.cosfo.mall.stock.model.dto;

import lombok.Data;

/**
 * 预分配订单项明细
 *
 * @author: <EMAIL>
 * @创建时间: 2023/4/10
 */
@Data
public class PreDistributionOrderItemDTO {
    /**
     * 商品项Id
     */
    private Long itemId;
    /**
     * 货品类型 0虚拟货品 1报价货品 2自营货品
     */
    private Integer goodsType;
    /**
     * 货品Id
     */
    private Long skuId;
    /**
     * 代理方货品Id
     */
    private Long agentSkuId;
    /**
     * 代理方sku编码
     */
    private String agentSku;
    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 供应商租户Id
     */
    private Long agentTenantId;

    /**
     * 商品预售开关 0-不可预售 1-可预售 默认值0
     */
    private Integer presaleSwitch;

    /**
     * sku归属租户id  自营货品为tenantId>1; 报价货品为1
     */
    private Long skuTenantId;
}
