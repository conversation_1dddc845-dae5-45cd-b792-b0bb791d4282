package com.cosfo.mall.stock.provider.impl;

import com.cosfo.mall.client.stock.provider.StockQueryProvider;
import com.cosfo.mall.client.stock.req.StockQueryReq;
import com.cosfo.mall.client.stock.resp.StockInfoResp;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.merchant.service.MerchantAddressService;
import com.cosfo.mall.stock.model.dto.StockDTO;
import com.cosfo.mall.stock.service.StockService;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 计划单生成订单
 *
 * @author: xiaowk
 * @time: 2024/2/20 下午4:59
 */
@Slf4j
@DubboService
public class StockQueryProviderImpl implements StockQueryProvider {

    @Resource
    private StockService stockService;
    @Resource
    private MerchantAddressService merchantAddressService;


    @Override
    public DubboResponse<Map<Long, StockInfoResp>> queryStock(@Valid StockQueryReq stockQueryReq) {

        LoginContextInfoDTO loginContextInfoDTO = new LoginContextInfoDTO();
        loginContextInfoDTO.setTenantId(stockQueryReq.getTenantId());
        loginContextInfoDTO.setStoreId(stockQueryReq.getStoreId());

        MerchantAddressDTO addressDto = merchantAddressService.queryDefaultAddressDTO(stockQueryReq.getStoreId(), stockQueryReq.getTenantId());
        Map<Long, StockDTO> stockDTOMap = stockService.queryStockAmount(loginContextInfoDTO, addressDto, stockQueryReq.getItemIds(), null);
        Map<Long, StockInfoResp> stockInfoRespMap = stockDTOMap.entrySet().stream()
            .collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> {
                    StockDTO stockDTO = entry.getValue();
                    StockInfoResp stockInfoResp = new StockInfoResp();
                    stockInfoResp.setItemId(stockDTO.getItemId());
                    stockInfoResp.setAmount(stockDTO.getAmount());
                    stockInfoResp.setQuantityDate(stockDTO.getQuantityDate());
                    stockInfoResp.setSupplySkuId(stockDTO.getSupplySkuId());
                    stockInfoResp.setSupplyTenantId(stockDTO.getSupplyTenantId());
                    return stockInfoResp;
                }
            ));
        return DubboResponse.getOK(stockInfoRespMap);
    }
}
