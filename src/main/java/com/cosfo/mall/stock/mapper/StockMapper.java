package com.cosfo.mall.stock.mapper;

import com.cosfo.mall.stock.model.po.Stock;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StockMapper {
    int deleteByPrimaryKey(Long id);

    int insert(Stock record);

    int insertSelective(Stock record);

    Stock selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Stock record);

    int updateByPrimaryKey(Stock record);

    /**
     * 查询库存信息
     * @param tenantId 租户信息
     * @param itemId itemId
     * @return 库存信息
     */
    Stock selectById(@Param("tenantId") Long tenantId, @Param("itemId") Long itemId);

    /**
     * 更新库存数量
     * @param id 主键
     * @param addAmount 增加数量
     */
    Integer increaseStock(@Param("id") Long id, @Param("addAmount") Integer addAmount);

    /**
     * 批量查询
     *
     * @param tenantId
     * @param itemIds
     * @return
     */
    List<Stock> batchQuery(@Param("tenantId") Long tenantId, @Param("itemIds") List<Long> itemIds);

    /**
     * 更新前查询
     *
     * @return
     */
    Stock preUpdateQuery(@Param("tenantId") Long tenantId, @Param("itemId") Long itemId);
}