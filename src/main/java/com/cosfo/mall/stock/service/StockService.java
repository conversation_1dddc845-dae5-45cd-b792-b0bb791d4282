package com.cosfo.mall.stock.service;

import com.cofso.item.client.enums.StockRecordType;
import com.cosfo.mall.merchant.model.dto.LoginContextInfoDTO;
import com.cosfo.mall.merchant.model.dto.MerchantAddressDTO;
import com.cosfo.mall.order.model.dto.OrderDTO;
import com.cosfo.mall.stock.model.dto.StockDTO;
import com.cosfo.mall.stock.model.dto.StockQueryDTO;
import com.cosfo.ordercenter.client.resp.aftersale.OrderAfterSaleResp;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/5/17  14:48
 */
public interface StockService {
    /**
     * 查询库存
     *
     * @param context      用户上下文
     * @param orderAddress 下单地址
     * @param itemIds      查询skuId
     * @param isCombineSub
     * @return 库存
     */
    Map<Long, StockDTO> queryStockAmount(LoginContextInfoDTO context, MerchantAddressDTO orderAddress, List<Long> itemIds,Boolean isCombineSub);

    /**
     * 冻结库存
     * @param context 用户登录信息
     * @param orderDto 订单信息
     */
    void lockStock(LoginContextInfoDTO context, OrderDTO orderDto);

    /**
     * 返还库存
     * @param tenantId 用户登录信息
     * @param orderDto 售后信息
     */
    void unlockStockByCancel(Long tenantId, OrderDTO orderDto);


    /**
     * 配送前退款 - 释放库存
     *
     * @param orderAfterSaleDTO
     * @return
     */
    void unlockStockByAfterSale(OrderAfterSaleResp orderAfterSaleDTO);

    /**
     * 处理本地库存
     * @param tenantId 用户信息
     * @param recordType 库存变动类型
     * @param skuId skuId
     * @param addAmount 增加数量
     * @param recordNo 记录编号
     */
    void increaseSelfStock(Long tenantId, StockRecordType recordType, Long skuId, Integer addAmount, String recordNo);

    /**
     * 处理本地库存
     * @param tenantId 用户信息
     * @param recordType 库存变动类型
     * @param skuId skuId
     * @param reduceAmount 增加数量
     * @param recordNo 记录编号
     */
    void decreaseSelfStock(Long tenantId, StockRecordType recordType, Long skuId, Integer reduceAmount, String recordNo);

    /**
     * 购物车/下单查询预占用库存
     *
     * @param stockQueryDTO
     * @return
     */
    Map<Long, StockDTO> preDistributionOrderOccupy(StockQueryDTO stockQueryDTO);

    /**
     * 购物车/下单查询预占用库存
     * @param stockQueryDTO
     * @param isOpenApi true-开放平台查询 开放平台下单查库存，需要传storeId
     * @return
     */
    Map<Long, StockDTO> preDistributionOrderOccupy(StockQueryDTO stockQueryDTO, boolean isOpenApi);

    /**
     * 自营仓订单占用库存
     *
     * @param orderDTO
     */
    void orderOccupyBySpecifyWarehouseAndSku(OrderDTO orderDTO);
}
